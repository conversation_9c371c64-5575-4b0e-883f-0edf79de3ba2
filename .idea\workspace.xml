<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="93a496fa-cf8f-43bf-ad29-a7d1fd79d5e3" name="Default" comment="">
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78-RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78ms-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/78ms-RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/83pv-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/90ms-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/90ms-RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/90msp-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/90msp-RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/90pv-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/90pv-RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Add-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Add-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Add-RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Add-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-0.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-1.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-3.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-4.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-5.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-6.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-CNS1-UCS2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-GB1-0.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-GB1-1.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-GB1-2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-GB1-3.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-GB1-4.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-GB1-5.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-GB1-UCS2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-0.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-1.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-3.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-4.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-5.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-6.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Japan1-UCS2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Korea1-0.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Korea1-1.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Korea1-2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Adobe-Korea1-UCS2.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/B5pc-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/B5pc-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/CNS-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/CNS-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/CNS1-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/CNS1-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/CNS2-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/CNS2-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/ETHK-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/ETHK-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/ETen-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/ETen-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/ETenms-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/ETenms-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Ext-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Ext-RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Ext-RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Ext-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GB-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GB-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GB-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GB-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBK-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBK-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBK2K-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBK2K-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBKp-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBKp-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBT-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBT-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBT-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBT-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBTpc-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBTpc-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBpc-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/GBpc-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKdla-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKdla-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKdlb-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKdlb-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKgccs-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKgccs-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKm314-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKm314-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKm471-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKm471-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKscs-B5-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/HKscs-B5-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Hankaku.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Hiragana.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSC-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSC-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSC-Johab-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSC-Johab-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSCms-UHC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSCms-UHC-HW-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSCms-UHC-HW-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSCms-UHC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSCpc-EUC-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/KSCpc-EUC-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Katakana.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/NWP-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/NWP-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/RKSJ-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/RKSJ-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/Roman.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UCS2-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UCS2-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UTF16-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UTF16-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UTF32-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UTF32-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UTF8-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniCNS-UTF8-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UCS2-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UCS2-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UTF16-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UTF16-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UTF32-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UTF32-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UTF8-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniGB-UTF8-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UCS2-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UCS2-HW-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UCS2-HW-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UCS2-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UTF16-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UTF16-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UTF32-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UTF32-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UTF8-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS-UTF8-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS2004-UTF16-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS2004-UTF16-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS2004-UTF32-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS2004-UTF32-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS2004-UTF8-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJIS2004-UTF8-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJISPro-UCS2-HW-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJISPro-UCS2-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJISPro-UTF8-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJISX0213-UTF32-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJISX0213-UTF32-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJISX02132004-UTF32-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniJISX02132004-UTF32-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UCS2-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UCS2-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UTF16-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UTF16-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UTF32-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UTF32-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UTF8-H.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/UniKS-UTF8-V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/V.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/WP-Symbol.bcmap" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/compressed.tracemonkey-pldi-09.pdf" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/findbarButton-next-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/findbarButton-next.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/findbarButton-previous-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/findbarButton-previous.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/grab.cur" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/grabbing.cur" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/loading-icon.gif" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/loading-small.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/secondaryToolbarButton-documentProperties.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/secondaryToolbarButton-firstPage.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/secondaryToolbarButton-handTool.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/secondaryToolbarButton-lastPage.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/secondaryToolbarButton-rotateCcw.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/secondaryToolbarButton-rotateCw.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/secondaryToolbarButton-selectTool.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/shadow.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/texture.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-bookmark.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-download.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-menuArrows.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-openFile.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-pageDown-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-pageDown.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-pageUp-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-pageUp.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-presentationMode.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-print.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-search.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-secondaryToolbarToggle-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-secondaryToolbarToggle.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-sidebarToggle-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-sidebarToggle.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-viewAttachments.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-viewOutline-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-viewOutline.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-viewThumbnail.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-zoomIn.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/toolbarButton-zoomOut.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/treeitem-collapsed-rtl.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/treeitem-collapsed.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/treeitem-expanded.png" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/<EMAIL>" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/as/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/el/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/gu-IN/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/he/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ka/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/kk/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/kn/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/pa-IN/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ta/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/te/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/.idea/vcs.xml" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/index.html" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/build/pdf.js" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/build/pdf.js.map" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/build/pdf.worker.js" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/build/pdf.worker.js.map" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/cmaps/LICENSE" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/debugger.js" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-check.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-comment.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-help.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-insert.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-key.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-newparagraph.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-noicon.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-note.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/images/annotation-paragraph.svg" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ach/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/af/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ak/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/an/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ar/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ast/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/az/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/be/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/bg/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/bn-BD/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/bn-IN/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/br/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/bs/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ca/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/cs/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/csb/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/cy/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/da/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/de/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/en-GB/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/en-US/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/en-ZA/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/eo/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/es-AR/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/es-CL/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/es-ES/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/es-MX/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/et/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/eu/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/fa/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ff/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/fi/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/fr/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/fy-NL/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ga-IE/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/gd/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/gl/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/hi-IN/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/hr/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/hu/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/hy-AM/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/id/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/is/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/it/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ja/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/km/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ko/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ku/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/lg/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/lij/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/locale.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/lt/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/lv/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/mai/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/mk/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ml/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/mn/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/mr/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ms/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/my/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/nb-NO/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/nl/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/nn-NO/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/nso/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/oc/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/or/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/pl/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/pt-BR/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/pt-PT/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/rm/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ro/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ru/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/rw/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/sah/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/si/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/sk/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/sl/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/son/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/sq/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/sr/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/sv-SE/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/sw/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ta-LK/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/th/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/tl/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/tn/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/tr/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/uk/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/ur/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/vi/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/wo/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/xh/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/zh-CN/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/zh-TW/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/locale/zu/viewer.properties" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/viewer.css" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/viewer.html" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/viewer.js" />
      <change type="NEW" beforePath="" afterPath="$PROJECT_DIR$/js/pdf/web/viewer.js.map" />
    </list>
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file leaf-file-name="index.html" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/index.html">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="255">
              <caret line="15" column="67" lean-forward="true" selection-start-line="15" selection-start-column="67" selection-end-line="15" selection-end-column="67" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/index.html" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="ProjectFrameBounds">
    <option name="x" value="-1933" />
    <option name="y" value="-121" />
    <option name="width" value="1863" />
    <option name="height" value="1002" />
  </component>
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="PDF.js" type="b2602c69:ProjectViewProjectNode" />
              <item name="PDF.js" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scratches" />
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="settings.editor.selected.configurable" value="reference.idesettings.emmet" />
    <property name="nodejs_interpreter_path" value="$USER_HOME$/.nvm/versions/node/v7.4.0/bin/node" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="HbShouldOpenHtmlAsHb" value="" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/js" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="93a496fa-cf8f-43bf-ad29-a7d1fd79d5e3" name="Default" comment="" />
      <created>1536742154307</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1536742154307</updated>
      <workItem from="1536742155817" duration="627000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="627000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-1933" y="-121" width="1863" height="1002" extended-state="0" />
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.24972856" sideWeight="0.5" order="0" side_tool="false" content_ui="combo" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="Docker" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="true" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="false" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="-1" side_tool="true" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager />
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/index.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="15" column="67" lean-forward="true" selection-start-line="15" selection-start-column="67" selection-end-line="15" selection-end-column="67" />
          <folding />
        </state>
      </provider>
    </entry>
  </component>
</project>